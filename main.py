#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
程序入口
用于初始化和启动应用程序。
"""

import os
import sys
import logging
import warnings
from pathlib import Path

from PySide6.QtWidgets import QApplication, QSplashScreen
from PySide6.QtCore import QCoreApplication, Qt, QTimer
from PySide6.QtGui import QIcon, QFont, QPixmap

from config import ConfigManager
from ui.main_window import MainWindow

# 设置日志
logging.basicConfig(
    level=logging.DEBUG,  # 使用DEBUG级别获取更多信息
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('app.log', encoding='utf-8', mode='w')  # 使用'w'模式覆盖旧日志
    ]
)
logger = logging.getLogger("Main")

def setup_environment():
    """
    设置环境变量
    设置Qt相关属性
    """
    # Windows系统下设置任务栏图标
    if sys.platform == 'win32':
        import ctypes
        app_id = 'ai.video.maker.app'  # 任务栏ID
        ctypes.windll.shell32.SetCurrentProcessExplicitAppUserModelID(app_id)

    # 设置应用程序信息
    QCoreApplication.setApplicationName("AI视频解说制作软件")
    QCoreApplication.setOrganizationName("AI Video Maker")
    QCoreApplication.setOrganizationDomain("ai-video-maker.com")
    QCoreApplication.setApplicationVersion("1.0.0")

def main():
    """主函数"""
    # 设置环境变量
    setup_environment()

    # 创建应用程序
    app = QApplication(sys.argv)

    # 设置应用程序样式
    app.setStyle("Fusion")

    # 设置应用程序字体
    font = QFont("Microsoft YaHei UI", 10)
    app.setFont(font)

    # 设置应用程序图标
    icon_path = Path("resources/icons/app.png")
    if icon_path.exists():
        app_icon = QIcon(str(icon_path))
        app.setWindowIcon(app_icon)
        logger.info(f"已设置应用程序图标: {icon_path}")
    else:
        app_icon = None
        logger.warning(f"图标文件不存在: {icon_path}")

    # 显示启动画面
    splash_path = Path("resources/splash/splash.png")
    if splash_path.exists():
        # 创建启动画面
        splash_pixmap = QPixmap(str(splash_path))
        splash = QSplashScreen(splash_pixmap, Qt.WindowStaysOnTopHint)

        # 设置启动画面的图标（如果有）
        if app_icon:
            splash.setWindowIcon(app_icon)

        # 显示启动画面
        splash.show()

        # 确保启动画面被渲染
        app.processEvents()

        # 设置字体
        font = QFont("Microsoft YaHei UI", 12, QFont.Bold)
        splash.setFont(font)

        # 显示初始化消息（使用HTML格式增强显示效果）
        splash.showMessage("<div style='background-color: rgba(0, 0, 0, 0.5); padding: 10px; border-radius: 5px;'>正在初始化应用程序...</div>",
                          Qt.AlignBottom | Qt.AlignHCenter, Qt.white)
        app.processEvents()

        logger.info(f"已显示启动画面: {splash_path}")
    else:
        splash = None
        logger.warning(f"启动画面图像不存在: {splash_path}")

    # 创建配置管理器
    if splash:
        splash.showMessage("<div style='background-color: rgba(0, 0, 0, 0.5); padding: 10px; border-radius: 5px;'>正在加载配置...</div>",
                          Qt.AlignBottom | Qt.AlignHCenter, Qt.white)
        app.processEvents()

    config_manager = ConfigManager()



    # 初始化其他模块
    if splash:
        splash.showMessage("<div style='background-color: rgba(0, 0, 0, 0.5); padding: 10px; border-radius: 5px;'>正在初始化模块...</div>",
                          Qt.AlignBottom | Qt.AlignHCenter, Qt.white)
        app.processEvents()

    # 创建主窗口（但不立即显示）
    if splash:
        splash.showMessage("<div style='background-color: rgba(0, 0, 0, 0.5); padding: 10px; border-radius: 5px;'>正在准备用户界面...</div>",
                          Qt.AlignBottom | Qt.AlignHCenter, Qt.white)
        app.processEvents()

    window = MainWindow(config_manager)

    # 完成加载
    if splash:
        splash.showMessage("<div style='background-color: rgba(0, 0, 0, 0.5); padding: 10px; border-radius: 5px;'>加载完成，正在启动...</div>",
                          Qt.AlignBottom | Qt.AlignHCenter, Qt.white)
        app.processEvents()

        # 延迟后显示主窗口（1500毫秒 = 1.5秒）
        QTimer.singleShot(1500, lambda: show_main_window(window, splash))
    else:
        # 如果没有启动画面，直接显示主窗口
        window.show()

    # 运行应用程序
    return app.exec()

def show_main_window(window, splash):
    """
    关闭启动画面并显示主窗口

    Args:
        window: 主窗口实例
        splash: 启动画面实例
    """
    # 显示主窗口
    window.show()

    # 关闭启动画面
    splash.finish(window)

if __name__ == "__main__":
    try:
        sys.exit(main())
    except Exception as e:
        logger.error(f"程序异常退出: {e}", exc_info=True)
        sys.exit(1)