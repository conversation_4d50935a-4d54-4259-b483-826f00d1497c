{"theme": "dark", "ai_models": [{"name": "GPT-4-Turbo", "api_type": "openai", "api_url": "https://api.openai.com/v1/chat/completions", "api_key": "", "model_name": "gpt-4-turbo", "temperature": 0.7, "max_tokens": 4000, "top_p": 1.0}, {"name": "Deepseek-Chat", "api_type": "openai", "api_url": "https://api.deepseek.com/v1/chat", "api_key": "", "model_name": "deepseek-chat", "temperature": 0.7, "max_tokens": 4000, "top_p": 1.0}, {"name": "Gemini 2.5 Flash 预览版 04-17", "api_type": "gemini", "api_url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro:generateContent", "api_key": "AIzaSyBvvyhRg940fS6sY8NgwUHvW8Rxbwi6ZqE", "model_name": "gemini-2.5-flash-preview-04-17", "temperature": 0.7, "top_p": 0.95, "max_output_tokens": 8192, "top_k": 40}, {"name": "Gemini 2.0 Flash", "api_type": "gemini", "api_url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent", "api_key": "AIzaSyCJMCiJKpbhtW0eqcM1216FKO4Y9cfwupE", "model_name": "gemini-2.0-flash", "temperature": 0.7, "top_p": 0.95, "max_output_tokens": 8192, "top_k": 40}], "current_model": "Gemini 2.5 Flash 预览版 04-17", "general_settings": {"video": {"resolution": "1920x1080", "bitrate": "5Mbps", "fps": 30, "format": "mp4", "codec": "h264", "preset": "faster", "use_hardware_acceleration": true, "memory_optimization": true, "parallel_processing": true, "chunk_size": 2}, "download": {"max_threads": 10, "download_dir": "./downloads", "resolution": "1080p", "bitrate": "20Mbps", "quality_priority": "优先分辨率", "format": "mp4", "orientation": "横屏优先", "subtitle_option": "不下载字幕"}, "voice": {"default_voice": "zh-CN-XiaoxiaoNeural", "rate": 1.1, "pitch": 1, "output_dir": "./audio", "max_threads": 8, "subtitle_format": "srt", "audio_format": "mp3", "audio_quality": "192k"}, "output": {"output_dir": "./output"}, "image": {"image_dir": "./images", "chrome_driver_path": "c:\\Users\\<USER>\\Desktop\\ai_video_maker\\drivers\\chromedriver_136.0.7051.0.exe", "proxy": "", "limit": 1, "size": ">1024*768", "color": "全部", "type": "全部", "format": "全部", "usage_rights": "全部", "safe_search": true}, "recent_files": {"last_project": "C:/Users/<USER>/Desktop/12.json"}}, "prompt_settings": {"screenplay_prompt": "我需要将以下文案分解成多个分镜，每个分镜时长不少于15秒，确保每个分镜包含完整的一个或多个句子。对于每个分镜，请提供以下信息：\n1. 原文：直接从文案中提取的文本。\n2. 中文提示词：提取关键元素和主题，便于搜索视频素材。\n3. 标签：3-5个关键词，用逗号分隔。\n\n请确保分镜划分合理，每个分镜主题集中，长度适中。文案内容如下：\n\n{text}", "translation_prompt": "请将以下中文提示词和标签翻译成英文，保持原意的同时确保翻译的准确性和自然度。\n\n中文提示词：{prompt}\n标签：{tags}\n\n请按以下格式输出：\n英文提示词：\n英文标签："}}